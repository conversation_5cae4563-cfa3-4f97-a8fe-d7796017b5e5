// Test script to check admin API endpoints and date formatting
// const axios = require('axios'); // Commented out since axios is not installed

const BASE_URL = 'http://localhost:5000/api';

// Test admin token (you'll need to get this from Firebase Auth)
const ADMIN_TOKEN = 'YOUR_ADMIN_TOKEN_HERE';

async function testAdminAPIs() {
  try {
    console.log('🔍 Testing Admin API endpoints...\n');

    const headers = {
      'Authorization': `Bearer ${ADMIN_TOKEN}`,
      'Content-Type': 'application/json'
    };

    // Test orders endpoint
    console.log('📋 Testing /api/bookings (orders)...');
    try {
      const ordersResponse = await axios.get(`${BASE_URL}/bookings?limit=5`, { headers });
      console.log('✅ Orders API Response:');
      console.log('- Total orders:', ordersResponse.data.orders?.length || 0);

      if (ordersResponse.data.orders && ordersResponse.data.orders.length > 0) {
        const firstOrder = ordersResponse.data.orders[0];
        console.log('- First order ID:', firstOrder.id);
        console.log('- First order createdAt:', firstOrder.createdAt);
        console.log('- First order createdAt type:', typeof firstOrder.createdAt);
        console.log('- First order service:', firstOrder.service?.name);
        console.log('- First order customer:', firstOrder.customerInfo?.name);
      }
    } catch (error) {
      console.log('❌ Orders API Error:', error.response?.data || error.message);
    }

    console.log('\n👥 Testing /api/users...');
    try {
      const usersResponse = await axios.get(`${BASE_URL}/users?limit=5`, { headers });
      console.log('✅ Users API Response:');
      console.log('- Total users:', usersResponse.data.users?.length || 0);

      if (usersResponse.data.users && usersResponse.data.users.length > 0) {
        const firstUser = usersResponse.data.users[0];
        console.log('- First user ID:', firstUser.id);
        console.log('- First user name:', firstUser.name);
        console.log('- First user createdAt:', firstUser.createdAt);
        console.log('- First user createdAt type:', typeof firstUser.createdAt);
      }
    } catch (error) {
      console.log('❌ Users API Error:', error.response?.data || error.message);
    }

    console.log('\n🤝 Testing /api/partners...');
    try {
      const partnersResponse = await axios.get(`${BASE_URL}/partners?limit=5`, { headers });
      console.log('✅ Partners API Response:');
      console.log('- Total partners:', partnersResponse.data?.length || 0);

      if (partnersResponse.data && partnersResponse.data.length > 0) {
        const firstPartner = partnersResponse.data[0];
        console.log('- First partner ID:', firstPartner.uid);
        console.log('- First partner name:', firstPartner.name);
        console.log('- First partner registeredAt:', firstPartner.registeredAt);
        console.log('- First partner registeredAt type:', typeof firstPartner.registeredAt);
      }
    } catch (error) {
      console.log('❌ Partners API Error:', error.response?.data || error.message);
    }

  } catch (error) {
    console.error('❌ General Error:', error.message);
  }
}

// Test date parsing functions
function testDateParsing() {
  console.log('\n🔍 Testing date parsing...\n');

  const testDates = [
    new Date(),
    'August 24, 2025 at 10:45:33 AM UTC+7',
    { seconds: 1724486733, nanoseconds: 0 },
    '2025-08-24T10:45:33.000Z',
    null,
    undefined,
    'invalid date'
  ];

  testDates.forEach((testDate, index) => {
    console.log(`Test ${index + 1}:`, testDate);
    console.log('Type:', typeof testDate);

    try {
      const parsed = new Date(testDate);
      console.log('Parsed:', parsed);
      console.log('Valid:', !isNaN(parsed.getTime()));
    } catch (error) {
      console.log('Error:', error.message);
    }
    console.log('---');
  });
}

if (require.main === module) {
  console.log('🚀 Starting Admin API Tests...\n');

  if (ADMIN_TOKEN === 'YOUR_ADMIN_TOKEN_HERE') {
    console.log('⚠️  Please set a valid ADMIN_TOKEN in the script');
    console.log('You can get this token from the browser console after logging in as admin');
    console.log('Look for the Authorization header in network requests\n');
  }

  testDateParsing();
  // testAdminAPIs(); // Uncomment when you have a valid token
}

module.exports = { testAdminAPIs, testDateParsing };
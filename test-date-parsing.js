// Test date parsing functions
const { parseDate, formatDate } = require('./client/src/utils/dateUtils.js');

// Test data from the actual API response
const testDates = [
  // Firestore timestamp format (from API)
  {"_seconds":1756007133,"_nanoseconds":94000000},
  
  // Regular JavaScript Date
  new Date(),
  
  // String formats
  "August 24, 2025 at 10:45:33 AM UTC+7",
  "2025-08-24T10:45:33.000Z",
  
  // Edge cases
  null,
  undefined,
  "invalid date",
  
  // Old Firestore format (without underscore)
  {"seconds":1756007133,"nanoseconds":94000000}
];

console.log('🔍 Testing date parsing functions...\n');

testDates.forEach((testDate, index) => {
  console.log(`Test ${index + 1}:`);
  console.log('Input:', testDate);
  console.log('Type:', typeof testDate);
  
  try {
    const parsed = parseDate(testDate);
    console.log('Parsed:', parsed);
    console.log('Formatted:', formatDate(testDate));
  } catch (error) {
    console.log('Error:', error.message);
  }
  console.log('---');
});

// Test the specific case from our API
console.log('\n🎯 Testing specific API response format:');
const apiResponse = {"_seconds":1756007133,"_nanoseconds":94000000};
console.log('API Response:', apiResponse);
console.log('Parsed:', parseDate(apiResponse));
console.log('Formatted:', formatDate(apiResponse));
console.log('Expected: 24/08/2025');

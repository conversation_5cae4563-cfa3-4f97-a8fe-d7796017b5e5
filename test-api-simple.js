// Simple test to check API responses without authentication
const http = require('http');

function makeRequest(path) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 5000,
      path: path,
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({ status: res.statusCode, data: jsonData });
        } catch (error) {
          resolve({ status: res.statusCode, data: data });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.end();
  });
}

async function testAPIs() {
  console.log('🔍 Testing API endpoints...\n');

  // Test basic endpoint
  try {
    console.log('📋 Testing /test endpoint...');
    const testResponse = await makeRequest('/test');
    console.log('Status:', testResponse.status);
    console.log('Response:', testResponse.data);
    console.log('---\n');
  } catch (error) {
    console.log('❌ Test endpoint error:', error.message);
  }

  // Test orders endpoint (will likely fail due to auth, but we can see the error)
  try {
    console.log('📋 Testing /api/bookings endpoint...');
    const ordersResponse = await makeRequest('/api/bookings');
    console.log('Status:', ordersResponse.status);
    console.log('Response:', ordersResponse.data);
    console.log('---\n');
  } catch (error) {
    console.log('❌ Orders endpoint error:', error.message);
  }

  // Test the new test-orders endpoint
  try {
    console.log('📋 Testing /test-orders endpoint...');
    const testOrdersResponse = await makeRequest('/test-orders');
    console.log('Status:', testOrdersResponse.status);
    console.log('Response:', JSON.stringify(testOrdersResponse.data, null, 2));
    console.log('---\n');
  } catch (error) {
    console.log('❌ Test orders endpoint error:', error.message);
  }
}

if (require.main === module) {
  testAPIs();
}
